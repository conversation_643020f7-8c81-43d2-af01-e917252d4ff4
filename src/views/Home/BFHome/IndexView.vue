<template>
  <div class="bf-home">
    <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirect-to-search="true" redirect-url="/search"
      @search="handleSearch" />

    <!-- Banner轮播 -->
    <div class="banner-container" v-if="headerBannerList.length > 0">
      <GoodsImageSwiper :media-list="headerBannerList" mode="banner" paginationType="fraction" :autoplay="true"
        :loop="true" height="200px" @image-click="handleBannerClick" />
    </div>

    <!-- 宫格菜单 -->
    <div class="grid-menu-container" v-if="gridMenuItems.length > 0">
      <GridMenu :items="gridMenuItems" :columns="5" :show-more="true" :max-items="10" @item-click="handleGridItemClick"
        @more-click="handleMoreClick" />
    </div>

    <Block title="各县销冠">
      <van-list :loading="limitedLoading" :finished="limitedFinished" finished-text="没有更多了" :offset="100"
        @load="handleLimitedLoadMore">
        <div class="waterfall-container" :style="{ minHeight: limitedList.length > 0 ? 'auto' : '200px' }">
          <Waterfall ref="limitedWaterfallRef" v-if="limitedList.length > 0" :list="limitedList"
            :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="300" :animationDelay="50"
            :backgroundColor="'transparent'" :lazyload="true">
            <template #default="{ item }">
              <GoodsCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
            </template>
          </Waterfall>
          <!-- 第一个模块的初始加载状态 -->
          <div v-else-if="limitedLoading && !limitedIsFirstLoadComplete" class="loading-placeholder">
            <van-loading type="spinner" size="24px">加载中...</van-loading>
          </div>
        </div>
      </van-list>
    </Block>

    <Block title="新上好物">
      <div class="horizontal-scroll-container" v-if="newerList.length > 0">
        <div class="horizontal-scroll-wrapper">
          <div class="goods-item" v-for="item in newerList" :key="item.goodsId" @click="handleGoodsClick(item)">
            <GoodsCard :goods-info="item" @click="handleGoodsClick(item)" />
          </div>
        </div>
      </div>
    </Block>

    <!-- 爆款好物模块：只有数据完全准备好才显示 -->
    <Block title="爆款好物">
      <van-list :loading="hotProductsLoading" :finished="hotProductsFinished" finished-text="没有更多了" :offset="100"
                @load="handleHotProductsLoadMore">
        <div class="waterfall-container" :style="{ minHeight: hotProductsList.length > 0 ? 'auto' : '200px' }">
          <Waterfall ref="hotProductsWaterfallRef" v-if="hotProductsList.length > 0" :list="hotProductsList"
                     :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="300" :animationDelay="50"
                     :backgroundColor="'transparent'" :lazyload="true">
            <template #default="{ item }">
              <GoodsCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
            </template>
          </Waterfall>
        </div>
      </van-list>
    </Block>
  </div>
</template>
<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import SearchHeader from '@components/Common/SearchHeader.vue'
import GoodsImageSwiper from '@/components/Common/GoodsImageSwiper.vue'
import GridMenu from '@components/Common/Home/GridMenu.vue'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import Block from '@components/Common/Home/Block.vue'
import GoodsCard from '@components/Common/Home/GoodsCard.vue'
import { fenToYuan } from '@utils/amount.js'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast } from 'vant'
const router = useRouter()
// 基础数据
const searchKeyword = ref('')
const headerBannerList = ref([])

// 10宫格菜单数据
const gridMenuItems = ref([])

// 各县销冠数据
const limitedList = ref([])
const limitedLoading = ref(false)
const limitedFinished = ref(false)
const limitedCurrentPage = ref(1)
const limitedPageSize = ref(10)
const limitedLastLoadTime = ref(0)
const limitedIsFirstLoadComplete = ref(false)

// 新上好物数据
const newerList = ref([])
const newerLoading = ref(false)
const newerIsComplete = ref(false)

// 爆款好物数据
const hotProductsList = ref([])
const hotProductsLoading = ref(false)
const hotProductsFinished = ref(false)
const hotProductsCurrentPage = ref(1)
const hotProductsPageSize = ref(10)
const hotProductsLastLoadTime = ref(0)
const hotProductsIsFirstLoadComplete = ref(false)

// 模块显示状态控制 - 只有完全准备好才显示
const moduleShowStates = ref({
  limited: true,     // 各县销冠始终显示（第一个模块）
  newer: false,      // 新上好物是否可以显示
  hotProducts: false // 爆款好物是否可以显示
})

// 模块数据准备状态
const moduleDataReady = ref({
  limited: false,    // 各县销冠数据是否准备完成
  newer: false,      // 新上好物数据是否准备完成
  hotProducts: false // 爆款好物数据是否准备完成
})

// 瀑布流配置
const breakpoints = ref({
  750: { rowPerView: 2 },
  550: { rowPerView: 2 },
  375: { rowPerView: 2 },
  290: { rowPerView: 1 }
})

// 瀑布流容器引用（保留以备将来使用）
const limitedWaterfallRef = ref(null)
const hotProductsWaterfallRef = ref(null)

// banner 过滤对应渠道的数据
const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}

// 获取头部banner列表
const getHeaderBannerList = async () => {
  const [err, json] = await getBannerInfo({ bizCode: getBizCode('QUERY'), showPage: 1 })
  console.warn(213132, err, json)
  if (!err) {
    // 转换数据格式为GoodsImageSwiper组件需要的格式
    const bannerData = channelFilterd(json).map(item => ({
      type: 'image',
      url: item.imgUrl,
      alt: item.bannerChName,
      linkUrl: item.url,
    }))
    console.warn(1231323123, bannerData)
    headerBannerList.value = bannerData
  }
}

// 获取宫格菜单列表
const getIconList = async () => {
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),
    channel: curChannelBiz.get(),
    showPage: 2
  })

  if (!err) {
    if (json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData.slice(0, 4)
    } else {
      gridMenuItems.value = []
    }
  }

}

// 搜索处理
const handleSearch = () => {
  // 搜索功能由 SearchHeader 组件处理
}

// Banner点击处理
const handleBannerClick = ({ item }) => {
  if (item.linkUrl) {
    // 处理banner点击跳转逻辑
    window.location.href = item.linkUrl
  }
}

// 宫格菜单点击处理
const handleGridItemClick = ({ item, index }) => {
  console.log('点击宫格菜单:', item, index)
  if (item.url) {
    // 这里可以使用 Vue Router 进行路由跳转
    // router.push(item.url)
    // 或者直接跳转
    window.location.href = item.url
  }
}

// 更多按钮点击处理
const handleMoreClick = () => {
  console.log('点击更多按钮')
  // 跳转到分类页面或显示更多菜单
  // router.push('/category')
}

// 获取各县销冠列表
const getLimitedList = async (isLoadMore = false) => {
  if (limitedLoading.value) return

  limitedLoading.value = true
  if (!isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: limitedCurrentPage.value,
    page_size: limitedPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_LIMITED_GOODS_ID
  })

  if (!isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    // 预加载图片以减少布局跳跃
    await preloadImages(newItems.map(item => item.image))

    if (isLoadMore) {
      limitedList.value = [...limitedList.value, ...newItems]
      // 等待DOM更新
      await nextTick()
      // 等待 Waterfall 重新计算布局完成
      await waitForWaterfallRender(limitedWaterfallRef, 3000)
    } else {
      limitedList.value = newItems
      await nextTick()

      // 等待 Waterfall 完全渲染完成
      await waitForWaterfallRender(limitedWaterfallRef)

      limitedIsFirstLoadComplete.value = true

      // 首次加载完成后，标记模块完成并触发下一个模块加载
      if (!moduleDataReady.value.limited) {
        moduleDataReady.value.limited = true
        console.log('各县销冠模块渲染完成，开始加载下一个模块')
        // 触发新上好物模块加载
        await loadNextModule()
      }
    }

    // 只有当返回的数据为空数组时才停止加载
    if (json.length === 0) {
      limitedFinished.value = true
    }

    // 只有在成功加载数据时才递增页码
    if (isLoadMore) {
      limitedCurrentPage.value++
    } else {
      // 首次加载成功后，将页码设置为2，为下次加载做准备
      limitedCurrentPage.value = 2
    }

  } else {
    // 没有数据或出错时，标记为加载完成
    limitedFinished.value = true
    // 即使失败也要触发下一个模块
    if (!moduleDataReady.value.limited) {
      moduleDataReady.value.limited = true
      await loadNextModule()
    }
  }

  limitedLoading.value = false
}
// 各县销冠加载更多处理
const handleLimitedLoadMore = () => {
  const now = Date.now()
  if (!limitedFinished.value && !limitedLoading.value && limitedIsFirstLoadComplete.value && (now - limitedLastLoadTime.value > 1000)) {
    limitedLastLoadTime.value = now
    getLimitedList(true)
  }
}

// 获取新上好物列表
const getNewerList = async () => {
  if (newerLoading.value) return

  newerLoading.value = true
  showLoadingToast()

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: 1,
    page_size: 10,
    id: import.meta.env.VITE_FP_HOME_PAGE_NEWER_GOODS_ID
  })

  closeToast()

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    newerList.value = newItems
    await nextTick()

    // 等待一小段时间确保横向滚动列表渲染完成
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  // 标记新上好物模块完成并显示模块，然后触发下一个模块加载
  if (!moduleDataReady.value.newer) {
    moduleDataReady.value.newer = true
    newerIsComplete.value = true

    // 显示新上好物模块
    moduleShowStates.value.newer = true
    console.log('新上好物模块渲染完成并显示，开始加载下一个模块')

    // 等待一帧确保DOM更新
    await nextTick()

    // 触发下一个模块加载
    await loadNextModule()
  }

  newerLoading.value = false
}

// 获取爆款好物列表
const getHotProductsList = async (isLoadMore = false) => {
  if (hotProductsLoading.value) return

  hotProductsLoading.value = true
  if (!isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: hotProductsCurrentPage.value,
    page_size: hotProductsPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_GUESS_GOODS_ID
  })

  if (!isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式为GoodsWaterfallItem组件需要的格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    // 预加载图片以减少布局跳跃
    await preloadImages(newItems.map(item => item.image))

    if (isLoadMore) {
      hotProductsList.value = [...hotProductsList.value, ...newItems]
      // 等待DOM更新
      await nextTick()
      // 等待 Waterfall 重新计算布局完成
      await waitForWaterfallRender(hotProductsWaterfallRef, 3000)
    } else {
      hotProductsList.value = newItems
      await nextTick()

      // 等待 Waterfall 完全渲染完成
      await waitForWaterfallRender(hotProductsWaterfallRef)

      hotProductsIsFirstLoadComplete.value = true

      // 首次加载完成后，标记模块完成并显示
      if (!moduleDataReady.value.hotProducts) {
        moduleDataReady.value.hotProducts = true

        // 显示爆款好物模块
        moduleShowStates.value.hotProducts = true
        console.log('爆款好物模块渲染完成并显示')
      }
    }

    // 只有当返回的数据为空数组时才停止加载
    if (json.length === 0) {
      hotProductsFinished.value = true
    }

    // 只有在成功加载数据时才递增页码
    if (isLoadMore) {
      hotProductsCurrentPage.value++
    } else {
      // 首次加载成功后，将页码设置为2，为下次加载做准备
      hotProductsCurrentPage.value = 2
    }
  } else {
    // 没有数据或出错时，标记为加载完成
    hotProductsFinished.value = true
    // 即使失败也要标记模块完成并显示
    if (!moduleDataReady.value.hotProducts) {
      moduleDataReady.value.hotProducts = true
      moduleShowStates.value.hotProducts = true
    }
  }

  hotProductsLoading.value = false
}

// 爆款好物加载更多处理
const handleHotProductsLoadMore = () => {
  const now = Date.now()
  if (!hotProductsFinished.value && !hotProductsLoading.value && hotProductsIsFirstLoadComplete.value && (now - hotProductsLastLoadTime.value > 1000)) {
    hotProductsLastLoadTime.value = now
    getHotProductsList(true)
  }
}

// 图片预加载函数，减少布局跳跃
const preloadImages = (imageUrls) => {
  return Promise.all(
    imageUrls.map(url => {
      return new Promise((resolve) => {
        if (!url) {
          resolve()
          return
        }
        const img = new Image()
        img.onload = () => resolve()
        img.onerror = () => resolve() // 即使加载失败也继续
        img.src = url
        // 设置超时，避免长时间等待
        setTimeout(() => resolve(), 3000)
      })
    })
  )
}

// 等待 Waterfall 渲染完成的函数
const waitForWaterfallRender = async (waterfallRef, timeout = 5000) => {
  return new Promise((resolve) => {
    let checkCount = 0
    const maxChecks = timeout / 100 // 每100ms检查一次

    const checkRender = () => {
      checkCount++

      // 如果没有瀑布流引用，直接完成
      if (!waterfallRef.value) {
        resolve()
        return
      }

      // 检查瀑布流是否已经渲染完成
      const waterfallEl = waterfallRef.value.$el || waterfallRef.value
      if (waterfallEl && waterfallEl.children && waterfallEl.children.length > 0) {
        // 检查是否所有子元素都有正确的位置
        const children = Array.from(waterfallEl.children)
        const allPositioned = children.every(child => {
          const style = window.getComputedStyle(child)
          return style.transform !== 'none' || (style.left !== 'auto' && style.top !== 'auto')
        })

        if (allPositioned) {
          console.log('Waterfall 渲染完成')
          resolve()
          return
        }
      }

      // 如果超时或达到最大检查次数，强制完成
      if (checkCount >= maxChecks) {
        console.log('Waterfall 渲染检查超时，强制继续')
        resolve()
        return
      }

      // 继续检查
      setTimeout(checkRender, 100)
    }

    // 开始检查
    setTimeout(checkRender, 100)
  })
}

const handleGoodsClick = (goodsInfo) => {
  console.log('点击商品:', goodsInfo)
  if (goodsInfo.goodsId) {
    // 跳转到商品详情页
    router.push(`/goodsdetail/${goodsInfo.goodsId}`)
  }
}

// 控制模块顺序加载的核心函数
const loadNextModule = async () => {
  // 如果各县销冠完成了，但新上好物还没开始，则加载新上好物
  if (moduleDataReady.value.limited && !moduleDataReady.value.newer) {
    console.log('开始加载新上好物模块')
    await getNewerList()
  }
  // 如果新上好物完成了，但爆款好物还没开始，则加载爆款好物
  else if (moduleDataReady.value.newer && !moduleDataReady.value.hotProducts) {
    console.log('开始加载爆款好物模块')
    await getHotProductsList(false)
  }
}



// 组件挂载时获取数据
onMounted(() => {
  getHeaderBannerList()
  getIconList()
  // 只加载第一个模块：各县销冠数据
  // 其他模块会在前一个模块完成后自动触发加载
  getLimitedList(false)
})
</script>

<style scoped lang="less">
.bf-home {
  .banner-container {
    margin: 8px 12px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .grid-menu-container {
    margin: 8px 0;
    background: #ffffff;
    border-radius: 12px;
    margin: 8px 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .horizontal-scroll-container {
    padding: 0 12px;

    .horizontal-scroll-wrapper {
      display: flex;
      gap: 12px;
      overflow-x: auto;
      padding-bottom: 8px;
      scroll-behavior: smooth;

      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        display: none;
      }

      -ms-overflow-style: none;
      scrollbar-width: none;

      .goods-item {
        flex: 0 0 160px;
        cursor: pointer;

        // 最后一个元素添加右边距
        &:last-child {
          margin-right: 12px;
        }
      }
    }
  }

  .waterfall-container {
    transition: min-height 0.3s ease;

    // 为瀑布流提供稳定的容器
    :deep(.vue-waterfall) {
      transition: height 0.3s ease;
    }
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
    color: #969799;
  }

  // 模块显示过渡动画
  .module-fade-enter-active {
    transition: all 0.5s ease-out;
  }

  .module-fade-enter-from {
    opacity: 0;
    transform: translateY(20px);
  }

  .module-fade-enter-to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
